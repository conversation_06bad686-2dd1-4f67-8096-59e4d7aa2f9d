package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/sirupsen/logrus"
)

// Logger 封装logrus的日志记录器
type Logger struct {
	*logrus.Logger
	name string
}

var (
	loggers = make(map[string]*Logger)
	mutex   sync.RWMutex
)

// GetLogger 获取指定名称的日志记录器，如果不存在则创建
func GetLogger(name string) *Logger {
	mutex.RLock()
	if logger, exists := loggers[name]; exists {
		mutex.RUnlock()
		return logger
	}
	mutex.RUnlock()

	mutex.Lock()
	defer mutex.Unlock()

	// 双重检查，防止并发创建
	if logger, exists := loggers[name]; exists {
		return logger
	}

	// 创建新的日志记录器
	logger := &Logger{
		Logger: logrus.New(),
		name:   name,
	}

	// 配置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置日志级别
	logger.SetLevel(logrus.InfoLevel)

	// 确保log目录存在
	logDir := "./log"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
	}

	// 设置日志输出到文件
	logFile := filepath.Join(logDir, fmt.Sprintf("%s.log", name))
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Printf("打开日志文件失败: %v\n", err)
		logger.SetOutput(os.Stdout)
	} else {
		logger.SetOutput(file)
	}

	// 添加钩子，同时输出到控制台
	logger.AddHook(&ConsoleHook{})

	loggers[name] = logger
	return logger
}

// ConsoleHook 控制台输出钩子
type ConsoleHook struct{}

// Levels 返回支持的日志级别
func (hook *ConsoleHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// Fire 执行钩子
func (hook *ConsoleHook) Fire(entry *logrus.Entry) error {
	line, err := entry.String()
	if err != nil {
		return err
	}
	fmt.Print(line)
	return nil
}

// Infof 格式化信息日志
func (l *Logger) Infof(format string, args ...interface{}) {
	l.Logger.Infof("[%s] %s", l.name, fmt.Sprintf(format, args...))
}

// Errorf 格式化错误日志
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.Logger.Errorf("[%s] %s", l.name, fmt.Sprintf(format, args...))
}

// Warnf 格式化警告日志
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.Logger.Warnf("[%s] %s", l.name, fmt.Sprintf(format, args...))
}

// Debugf 格式化调试日志
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.Logger.Debugf("[%s] %s", l.name, fmt.Sprintf(format, args...))
}
