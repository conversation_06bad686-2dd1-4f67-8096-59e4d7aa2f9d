package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"
)

// TestResult 测试结果结构体
type TestResult struct {
	TotalRequests   int           `json:"total_requests"`
	SuccessRequests int           `json:"success_requests"`
	FailedRequests  int           `json:"failed_requests"`
	TotalTime       time.Duration `json:"total_time"`
	AverageTime     time.Duration `json:"average_time"`
	RequestsPerSec  float64       `json:"requests_per_second"`
}

// APIResponse API响应结构体
type APIResponse struct {
	Items      []interface{} `json:"items"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

// performRequest 执行单个HTTP请求
func performRequest(url string, results chan<- time.Duration, wg *sync.WaitGroup) {
	defer wg.Done()

	start := time.Now()
	resp, err := http.Get(url)
	if err != nil {
		results <- -1 // 表示失败
		return
	}
	defer resp.Body.Close()

	// 读取响应体以确保完整处理
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		results <- -1 // 表示失败
		return
	}

	duration := time.Since(start)
	results <- duration
}

// runConcurrencyTest 运行并发测试
func runConcurrencyTest(url string, totalRequests, concurrency int) TestResult {
	fmt.Printf("开始并发测试: %d 个请求，%d 个并发连接\n", totalRequests, concurrency)
	fmt.Printf("目标URL: %s\n", url)

	results := make(chan time.Duration, totalRequests)
	var wg sync.WaitGroup

	startTime := time.Now()

	// 控制并发数
	semaphore := make(chan struct{}, concurrency)

	for i := 0; i < totalRequests; i++ {
		wg.Add(1)
		go func() {
			semaphore <- struct{}{} // 获取信号量
			performRequest(url, results, &wg)
			<-semaphore // 释放信号量
		}()
	}

	wg.Wait()
	close(results)

	totalTime := time.Since(startTime)

	// 统计结果
	var successCount, failedCount int
	var totalDuration time.Duration

	for duration := range results {
		if duration == -1 {
			failedCount++
		} else {
			successCount++
			totalDuration += duration
		}
	}

	var averageTime time.Duration
	if successCount > 0 {
		averageTime = totalDuration / time.Duration(successCount)
	}

	requestsPerSec := float64(successCount) / totalTime.Seconds()

	return TestResult{
		TotalRequests:   totalRequests,
		SuccessRequests: successCount,
		FailedRequests:  failedCount,
		TotalTime:       totalTime,
		AverageTime:     averageTime,
		RequestsPerSec:  requestsPerSec,
	}
}

// printResults 打印测试结果
func printResults(result TestResult) {
	fmt.Println("\n=== 测试结果 ===")
	fmt.Printf("总请求数: %d\n", result.TotalRequests)
	fmt.Printf("成功请求数: %d\n", result.SuccessRequests)
	fmt.Printf("失败请求数: %d\n", result.FailedRequests)
	fmt.Printf("总耗时: %v\n", result.TotalTime)
	fmt.Printf("平均响应时间: %v\n", result.AverageTime)
	fmt.Printf("每秒请求数: %.2f\n", result.RequestsPerSec)
	fmt.Printf("成功率: %.2f%%\n", float64(result.SuccessRequests)/float64(result.TotalRequests)*100)
}

// testAPIResponse 测试API响应内容
func testAPIResponse(url string) {
	fmt.Printf("\n=== 测试API响应 ===\n")
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("返回记录数: %d\n", len(apiResp.Items))
	fmt.Printf("总记录数: %d\n", apiResp.Total)
	fmt.Printf("当前页: %d\n", apiResp.Page)
	fmt.Printf("页面大小: %d\n", apiResp.PageSize)
	fmt.Printf("总页数: %d\n", apiResp.TotalPages)

	if len(apiResp.Items) > 0 {
		fmt.Printf("第一条记录: %+v\n", apiResp.Items[0])
	}
}

func TestPerformance(m *testing.M) {
	baseURL := "http://localhost:8000"
	apiURL := baseURL + "/api/info?page=1&page_size=10"

	// 首先测试API是否可用
	fmt.Println("检查API服务是否可用...")
	resp, err := http.Get(baseURL)
	if err != nil {
		fmt.Printf("无法连接到API服务: %v\n", err)
		fmt.Println("请确保服务器正在运行: go run main.go")
		return
	}
	resp.Body.Close()

	if resp.StatusCode != 200 {
		fmt.Printf("API服务返回错误状态码: %d\n", resp.StatusCode)
		return
	}

	fmt.Println("API服务可用!")

	// 测试API响应内容
	testAPIResponse(apiURL)

	// 运行不同的并发测试
	testCases := []struct {
		requests    int
		concurrency int
	}{
		{100, 10},   // 100个请求，10个并发
		{500, 50},   // 500个请求，50个并发
		{1000, 100}, // 1000个请求，100个并发
	}

	for _, tc := range testCases {
		fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
		result := runConcurrencyTest(apiURL, tc.requests, tc.concurrency)
		printResults(result)

		// 等待一下再进行下一个测试
		time.Sleep(2 * time.Second)
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("性能测试完成!")
}
