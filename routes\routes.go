package routes

import (
	"net/http"

	"train_expert_system/middleware"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes() *gin.Engine {
	// 创建Gin引擎
	router := gin.Default()

	// 添加中间件
	router.Use(middleware.CORS())

	// API路由组
	api := router.Group("/api")
	{
		// info相关路由
		api.GET("/info", infoController.GetInfo)
		api.POST("/info", infoController.CreateInfo)
	}

	// 健康检查路由
	router.GET("/health", func(c *gin.Context) {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "服务运行正常",
		})
	})

	return router
}
