package models

// Info 表模型，对应数据库中的info表
type Info struct {
	ID      uint   `json:"id" gorm:"primaryKey;autoIncrement"`
	Name    string `json:"name" gorm:"column:name"`
	Value   int    `json:"value" gorm:"column:value"`
	Message string `json:"message" gorm:"column:message"`
}

// TableName 指定表名
func (Info) TableName() string {
	return "info"
}

// InfoResponse 用于API响应的结构体
type InfoResponse struct {
	Items      []Info `json:"items"`
	Total      int64  `json:"total"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
	TotalPages int    `json:"total_pages"`
}

// PaginationParams 分页参数
type PaginationParams struct {
	Page     int `form:"page" binding:"min=1"`
	PageSize int `form:"page_size" binding:"min=1,max=10000"`
}
