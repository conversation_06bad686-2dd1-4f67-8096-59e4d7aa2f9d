# 列车信息查询API - Golang版本

这是一个使用Golang、Gin框架和GORM的列车信息查询API系统，从Python FastAPI项目移植而来。

## 项目结构

```
train_expert_system_go/
├── main.go                     # 主程序入口
├── go.mod                      # Go模块文件
├── models/                     # 数据模型
│   └── info.go                # Info模型定义
├── database/                   # 数据库配置
│   └── database.go            # 数据库连接和配置
├── services/                   # 业务逻辑层
│   ├── info_service.go        # 真实数据库服务
│   └── mock_info_service.go   # 模拟数据库服务（用于性能测试）
├── controllers/                # 控制器层
│   └── info_controller.go     # Info控制器
├── routes/                     # 路由配置
│   └── routes.go              # 路由设置
├── middleware/                 # 中间件
│   └── cors.go                # CORS中间件
├── utils/                      # 工具类
│   └── logger.go              # 日志工具
└── log/                        # 日志文件目录
```

## 功能特性

- ✅ RESTful API设计
- ✅ 分页查询支持
- ✅ CORS跨域支持
- ✅ 结构化日志记录
- ✅ 优雅关闭
- ✅ 数据库连接池
- ✅ 模拟数据服务（用于性能测试）

## 安装和运行

### 1. 安装依赖

```bash
cd train_expert_system_go
go mod tidy
```

### 2. 配置数据库

修改 `database/database.go` 中的数据库连接参数：

```go
const (
    DBUsername = "root"
    DBPassword = "123456"
    DBHost     = "localhost"
    DBPort     = "3306"
    DBName     = "trains"
)
```

### 3. 运行服务

```bash
go run main.go
```

服务将在 `http://localhost:8000` 启动。

## API接口

### 1. 根路径
- **GET** `/`
- 返回欢迎信息

### 2. 查询信息列表
- **GET** `/api/info?page=1&page_size=10`
- 参数：
  - `page`: 页码（默认1）
  - `page_size`: 每页记录数（默认10，最大10000）
- 返回分页数据

### 3. 创建信息
- **POST** `/api/info`
- 请求体：
```json
{
    "name": "示例名称",
    "value": 100,
    "message": "示例消息"
}
```

### 4. 健康检查
- **GET** `/health`
- 返回服务状态

## 性能测试

项目包含模拟数据库服务，可用于并发性能测试：

### 使用模拟服务

在 `controllers/info_controller.go` 中，将：
```go
service: services.NewInfoDB(),
```

替换为：
```go
service: services.NewMockInfoDB(),
```

### 压力测试示例

使用Apache Bench进行测试：
```bash
# 测试100个并发用户，总共1000个请求
ab -n 1000 -c 100 http://localhost:8000/api/info
```

使用curl进行简单测试：
```bash
# 查询第一页数据
curl "http://localhost:8000/api/info?page=1&page_size=10"

# 创建新记录
curl -X POST "http://localhost:8000/api/info" \
     -H "Content-Type: application/json" \
     -d '{"name":"测试","value":123,"message":"测试消息"}'
```

## 日志

日志文件保存在 `log/` 目录下：
- `main.log`: 主程序日志
- `api.log`: API请求日志
- `info_service.log`: 数据库服务日志
- `mock_info_service.log`: 模拟服务日志

## 开发说明

### 添加新的API接口

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `controllers/` 中实现控制器
4. 在 `routes/routes.go` 中添加路由

### 数据库迁移

项目使用GORM，可以通过以下方式进行数据库迁移：

```go
// 在main.go中添加
db := database.GetDB()
db.AutoMigrate(&models.Info{})
```

## 与Python版本的对比

| 特性 | Python版本 | Golang版本 |
|------|------------|------------|
| Web框架 | FastAPI | Gin |
| ORM | SQLModel | GORM |
| 日志 | uvicorn + custom | logrus |
| 并发模型 | 异步 | goroutine |
| 性能 | 高 | 更高 |
| 内存占用 | 中等 | 低 |
