package db

import (
	"fmt"
	"time"

	"train_expert_system/models"
	"train_expert_system/utils"

	"gorm.io/gorm"
)

// InfoDB 信息数据库操作结构体
type InfoDB struct {
	db     *gorm.DB
	logger *utils.Logger
}

// NewInfoDB 创建新的信息服务实例
func NewInfoDB() *InfoDB {
	return &InfoDB{
		db:     GetDB(),
		logger: utils.GetLogger("database"),
	}
}

// Query 查询info表数据，支持分页
func (db *InfoDB) Query(limit, offset int, countTotal bool) ([]models.Info, int64, error) {
	startTime := time.Now()
	db.logger.Infof("开始查询info表，限制 %d 行，偏移量 %d", limit, offset)

	var results []models.Info
	var total int64

	// 查询数据
	query := db.db.Model(&models.Info{}).Offset(offset).Limit(limit)
	if err := query.Find(&results).Error; err != nil {
		db.logger.Errorf("查询失败: %v", err)
		return nil, 0, fmt.Errorf("查询数据失败: %v", err)
	}

	// 如果需要，获取总记录数
	if countTotal {
		if err := db.db.Model(&models.Info{}).Count(&total).Error; err != nil {
			db.logger.Errorf("获取总记录数失败: %v", err)
			return nil, 0, fmt.Errorf("获取总记录数失败: %v", err)
		}
	}

	duration := time.Since(startTime)
	db.logger.Infof("查询成功，返回 %d 条记录，耗时 %.3f 秒", len(results), duration.Seconds())

	return results, total, nil
}

// Insert 插入单条记录
func (s *InfoDB) Insert(info *models.Info) error {
	startTime := time.Now()
	s.logger.Infof("开始插入单条记录，name=%s", info.Name)

	if err := s.db.Create(info).Error; err != nil {
		s.logger.Errorf("插入失败: %v", err)
		return fmt.Errorf("插入记录失败: %v", err)
	}

	duration := time.Since(startTime)
	s.logger.Infof("插入成功，ID=%d，耗时 %.3f 秒", info.ID, duration.Seconds())

	return nil
}

// BatchInsert 批量插入记录
func (db *InfoDB) BatchInsert(items []models.Info) error {
	startTime := time.Now()
	count := len(items)
	db.logger.Infof("开始批量插入 %d 条记录", count)

	// 使用事务进行批量插入
	err := db.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(items, 100).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		db.logger.Errorf("批量插入失败: %v", err)
		return fmt.Errorf("批量插入失败: %v", err)
	}

	duration := time.Since(startTime)
	perRecord := duration.Seconds() / float64(count)
	db.logger.Infof("批量插入成功，共 %d 条记录，总耗时 %.3f 秒，平均每条 %.5f 秒",
		count, duration.Seconds(), perRecord)

	return nil
}
